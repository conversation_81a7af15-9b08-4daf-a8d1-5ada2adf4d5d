{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.14/MicrosoftTeams.schema.json", "manifestVersion": "1.14", "version": "1.0.6", "id": "652a2b91-23ab-4057-a224-a5dd85ca4bd4", "packageName": "com.contoso.voicemessaging.dev", "developer": {"name": "CVM-Dev", "websiteUrl": "https://dev-cvm.citrusinformatics.com/", "privacyUrl": "https://dev-cvm.citrusinformatics.com/privacy", "termsOfUseUrl": "https://dev-cvm.citrusinformatics.com/terms"}, "name": {"short": "CVM-Dev", "full": "Voice Messaging for Microsoft Teams"}, "description": {"short": "Send voice messages in Teams", "full": "Record and send voice messages directly within Microsoft Teams."}, "icons": {"outline": "outline.jpg", "color": "color.jpg"}, "accentColor": "#FFFFFF", "configurableTabs": [], "staticTabs": [{"entityId": "voice-messages", "name": "Voice Messages", "contentUrl": "https://dev-cvm.citrusinformatics.com/", "websiteUrl": "https://dev-cvm.citrusinformatics.com/", "scopes": ["personal"]}], "bots": [{"botId": "652a2b91-23ab-4057-a224-a5dd85ca4bd4", "scopes": ["personal", "team", "groupChat"], "supportsFiles": true, "isNotificationOnly": false, "needsChannelSelector": false, "commandLists": [{"scopes": ["personal", "team", "groupChat"], "commands": [{"title": "Help", "description": "Shows help information"}]}]}], "composeExtensions": [{"botId": "652a2b91-23ab-4057-a224-a5dd85ca4bd4", "canUpdateConfiguration": false, "commands": [{"id": "recordVoiceMessage", "context": ["compose", "commandBox", "message"], "type": "action", "title": "Voice Message", "description": "Record and send voice messages", "initialRun": true, "fetchTask": true, "taskInfo": {"title": "Record Voice Message", "width": "350", "height": "380", "url": "https://dev-cvm.citrusinformatics.com/index.html"}, "parameters": [{"name": "voiceMessage", "title": "Voice Message", "description": "Record a voice message"}]}]}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["cvm.citrusinformatics.com", "dev-cvm.citrusinformatics.com", "*.citrusinformatics.com", "token.botframework.com", "*.login.microsoftonline.com", "*.sharepoint.com", "*.teams.microsoft.com", "smba.trafficmanager.net", "graph.microsoft.com", "login.microsoftonline.com"], "webApplicationInfo": {"id": "652a2b91-23ab-4057-a224-a5dd85ca4bd4", "resource": "api://dev-cvm.citrusinformatics.com/652a2b91-23ab-4057-a224-a5dd85ca4bd4"}, "devicePermissions": ["media"], "showLoadingIndicator": true}