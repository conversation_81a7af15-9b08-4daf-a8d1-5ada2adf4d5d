# Teams Voice Messaging Extension

A Microsoft Teams extension that enables voice messaging capabilities in the desktop application.

## Features

- Record voice messages directly within Teams
- Preview recordings before sending
- Support for both light and dark Teams themes
- Automatic message delivery to recipients
- Secure storage using Azure Blob Storage
- Real-time status updates

## Prerequisites

- Node.js 14.x or later
- Microsoft 365 Developer account
- Azure subscription
- Teams admin access for deployment

## Environment Configuration

This project supports separate development and production environments with automatic configuration injection.

### Environment Files

The project uses two environment files:
- `.env.development` - Development environment configuration
- `.env.production` - Production environment configuration

### Required Environment Variables

```bash
# Azure/Teams Configuration
TENANT_ID=your_tenant_id
CLIENT_ID=your_client_id
CLIENT_SECRET=your_client_secret
BOT_ID=your_bot_id
BOT_PASSWORD=your_bot_password
TEAMS_APP_ID=your_teams_app_id

# Server Configuration
PORT=3001
BOT_ENDPOINT=https://your-domain.com/api/messages
BASE_URL=https://your-domain.com
SOCKET_URL=https://your-domain.com
REDIRECT_URI=https://your-domain.com/auth-end.html
```

### Environment Setup

1. **Development Environment**:
   ```bash
   # Update .env.development with your development values
   npm run inject:dev
   ```

2. **Production Environment**:
   ```bash
   # Update .env.production with your production values
   npm run inject:prod
   ```

### Azure App Registration

1. Register your application in Azure Active Directory:
   - Go to Azure Portal > Azure Active Directory > App registrations
   - Create separate registrations for development and production
   - Add necessary API permissions for Microsoft Graph:
     - `Files.ReadWrite`
     - `Sites.ReadWrite.All`
     - `ChatMessage.Send`
     - `User.Read`
   - Note down the Client ID, Client Secret, and Tenant ID for each environment

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd teams-voice-messaging
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment:
   ```bash
   # For development
   npm run inject:dev
   npm run dev

   # For production
   npm run inject:prod
   npm run prod
   ```

## Available Scripts

- `npm run dev` - Start development server with auto-reload
- `npm run prod` - Start production server
- `npm run inject:dev` - Inject development environment variables
- `npm run inject:prod` - Inject production environment variables
- `npm run restore` - Restore placeholder values in files
- `npm run package:dev` - Package app for development deployment
- `npm run package:prod` - Package app for production deployment

## Environment Management

The project uses an automated environment injection system:

1. **Files with placeholders**: `manifest.json`, `index.html`, `server.js`
2. **Automatic replacement**: Environment-specific values are injected before running
3. **Clean switching**: Placeholders are restored before each injection

### Manual Environment Operations

```bash
# Restore all files to placeholder state
npm run restore

# Inject specific environment
npm run inject:dev    # Development
npm run inject:prod   # Production

# Package with environment
npm run package:dev   # Development package
npm run package:prod  # Production package
```

## Teams App Setup

1. Package the Teams app:
   - Update the manifest.json with your app details
   - Create a zip file containing:
     - manifest.json
     - outline.jpg
     - color.jpg

2. Upload to Teams:
   - Go to Teams Admin Center
   - Navigate to Teams apps > Manage apps
   - Click "Upload new app"
   - Select your zip file

## Usage

1. Access the voice messaging tab in Teams
2. Click the record button to start recording
3. Preview your recording
4. Send or discard the message
5. Recipients will receive a notification with the voice message

## Security Considerations

- All communication is encrypted using HTTPS
- Microsoft Graph API
- User permissions are validated for each operation

## Troubleshooting

Common issues and solutions:

1. Recording not working:
   - Check microphone permissions in browser
   - Ensure Teams has necessary permissions

2. Messages not sending:
   - Check network connectivity
   - Confirm Microsoft Graph permissions

3. App not loading:
   - Verify manifest configuration
   - Check browser console for errors
   - Confirm Teams admin approval

## Support

For issues and feature requests, please contact your Teams administrator or open an issue in the repository.

## License

