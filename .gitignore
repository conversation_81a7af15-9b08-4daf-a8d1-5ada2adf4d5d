# Ignore node_modules and dependency folders
node_modules/
dist/

# Ignore logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore environment variables (keep .env.development and .env.production for templates)
.env
.env.local
.env.development
.env.test.local
.env.production

# Keep environment templates but ignore sensitive production values
# Note: .env.development and .env.production are tracked for configuration templates
# Make sure to update .env.production with actual production values before deployment

# Ignore certificate and key files
*.crt
*.key

# Ignore build artifacts
build/
temp/

# Ignore editor and system files
.vscode/
.idea/
.DS_Store
Thumbs.db