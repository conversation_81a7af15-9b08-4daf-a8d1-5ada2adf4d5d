// Configuration file for environment-specific values
// This file is automatically generated by the environment injection script
// Do not edit manually - use npm run inject:dev or npm run inject:prod

window.APP_CONFIG = {
    // Azure/Teams Configuration
    TENANT_ID: 'c82dd0b5-78de-4e84-b411-1cfc79f87740',
    CLIENT_ID: '652a2b91-23ab-4057-a224-a5dd85ca4bd4',
    BOT_ID: '652a2b91-23ab-4057-a224-a5dd85ca4bd4',
    TEAMS_APP_ID: '88253aef-2912-4c10-b594-9ea5d7472f7b',
    
    // Server Configuration
    BASE_URL: 'https://dev-cvm.citrusinformatics.com',
    SOCKET_URL: 'https://dev-cvm.citrusinformatics.com',
    REDIRECT_URI: 'https://dev-cvm.citrusinformatics.com/auth-end.html',
    RESOURCE_URL: 'dev-cvm.citrusinformatics.com',
    
    // Authentication Configuration
    AUTH_URL: 'https://login.microsoftonline.com/c82dd0b5-78de-4e84-b411-1cfc79f87740/oauth2/v2.0/authorize',
    GRAPH_SCOPES: 'https://graph.microsoft.com/Files.ReadWrite https://graph.microsoft.com/Sites.ReadWrite.All https://graph.microsoft.com/ChatMessage.Send',
    
    // API Endpoints
    BOT_ENDPOINT: 'https://dev-cvm.citrusinformatics.com/api/messages',
    
    // Helper methods
    getAuthUrl: function() {
        return `${this.AUTH_URL}?` +
               `client_id=${this.CLIENT_ID}&` +
               `response_type=token&` +
               `redirect_uri=${encodeURIComponent(this.REDIRECT_URI)}&` +
               `scope=${encodeURIComponent(this.GRAPH_SCOPES)}`;
    },
    
    getResourceUrl: function() {
        return `api://${this.RESOURCE_URL}/${this.CLIENT_ID}`;
    }
};

// Freeze the configuration to prevent accidental modifications
Object.freeze(window.APP_CONFIG);

console.log('[config.js] Configuration loaded:', {
    environment: window.APP_CONFIG.BASE_URL.includes('dev-') ? 'development' : 'production',
    baseUrl: window.APP_CONFIG.BASE_URL,
    clientId: window.APP_CONFIG.CLIENT_ID
});
