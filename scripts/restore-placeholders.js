// scripts/restore-placeholders.js
// Restores placeholder values in files for environment injection

const fs = require('fs');
const path = require('path');

const files = [
    {
        path: path.resolve(__dirname, '../manifest/manifest.json'),
        replacements: [
            { pattern: /652a2b91-23ab-4057-a224-a5dd85ca4bd4/g, placeholder: '__CLIENT_ID__' },
            { pattern: /f984ebaf-4c50-4de8-8687-80672674ab06/g, placeholder: '__CLIENT_ID__' },
            { pattern: /88253aef-2912-4c10-b594-9ea5d7472f7b/g, placeholder: '__TEAMS_APP_ID__' },
            { pattern: /https:\/\/dev-cvm\.citrusinformatics\.com/g, placeholder: '__BASE_URL__' },
            { pattern: /https:\/\/cvm\.citrusinformatics\.com/g, placeholder: '__BASE_URL__' },
            { pattern: /api:\/\/dev-cvm\.citrusinformatics\.com\/652a2b91-23ab-4057-a224-a5dd85ca4bd4/g, placeholder: 'api://__BASE_URL__/__CLIENT_ID__' },
            { pattern: /api:\/\/cvm\.citrusinformatics\.com\/f984ebaf-4c50-4de8-8687-80672674ab06/g, placeholder: 'api://__BASE_URL__/__CLIENT_ID__' }
        ]
    },
    {
        path: path.resolve(__dirname, '../public/index.html'),
        replacements: [
            { pattern: /https:\/\/dev-cvm\.citrusinformatics\.com/g, placeholder: '__SOCKET_URL__' },
            { pattern: /https:\/\/cvm\.citrusinformatics\.com/g, placeholder: '__SOCKET_URL__' },
            { pattern: /c82dd0b5-78de-4e84-b411-1cfc79f87740/g, placeholder: '__TENANT_ID__' },
            { pattern: /652a2b91-23ab-4057-a224-a5dd85ca4bd4/g, placeholder: '__CLIENT_ID__' },
            { pattern: /f984ebaf-4c50-4de8-8687-80672674ab06/g, placeholder: '__CLIENT_ID__' },
            { pattern: /https:\/\/dev-cvm\.citrusinformatics\.com\/auth-end\.html/g, placeholder: '__REDIRECT_URI__' },
            { pattern: /https:\/\/cvm\.citrusinformatics\.com\/auth-end\.html/g, placeholder: '__REDIRECT_URI__' }
        ]
    },
    {
        path: path.resolve(__dirname, '../src/server.js'),
        replacements: [
            { pattern: /https:\/\/dev-cvm\.citrusinformatics\.com/g, placeholder: '__BASE_URL__' },
            { pattern: /https:\/\/cvm\.citrusinformatics\.com/g, placeholder: '__BASE_URL__' }
        ]
    }
];

function restorePlaceholders() {
    console.log('[restore-placeholders] Restoring placeholder values...');
    
    files.forEach(file => {
        if (!fs.existsSync(file.path)) {
            console.warn(`[restore-placeholders] File not found: ${file.path}`);
            return;
        }
        
        let content = fs.readFileSync(file.path, 'utf8');
        let replacementCount = 0;
        
        file.replacements.forEach(({ pattern, placeholder }) => {
            const matches = content.match(pattern);
            if (matches) {
                content = content.replace(pattern, placeholder);
                replacementCount += matches.length;
            }
        });
        
        fs.writeFileSync(file.path, content, 'utf8');
        console.log(`[restore-placeholders] Restored ${replacementCount} placeholders in ${path.basename(file.path)}`);
    });
    
    console.log('[restore-placeholders] ✅ Placeholder restoration complete');
}

if (require.main === module) {
    restorePlaceholders();
}

module.exports = { restorePlaceholders };
