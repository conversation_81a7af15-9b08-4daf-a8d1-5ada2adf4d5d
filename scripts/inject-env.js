// scripts/inject-env.js
// Usage: node scripts/inject-env.js [development|production]

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const { restorePlaceholders } = require('./restore-placeholders');

const env = process.argv[2] || 'development';
const envFile = env === 'production' ? '.env.production' : '.env.development';
const envPath = path.resolve(__dirname, '../', envFile);
const manifestPath = path.resolve(__dirname, '../manifest/manifest.json');
const indexPath = path.resolve(__dirname, '../public/index.html');
const serverPath = path.resolve(__dirname, '../src/server.js');
const authEndPath = path.resolve(__dirname, '../public/auth-end.html');
const configPath = path.resolve(__dirname, '../public/config.js');

console.log(`[inject-env] Loading environment: ${env}`);
console.log(`[inject-env] Environment file: ${envPath}`);

// Load env vars
const result = dotenv.config({ path: envPath });
if (result.error) {
  console.error(`Failed to load env file: ${envPath}`);
  console.error('Make sure the environment file exists and is readable.');
  process.exit(1);
}
const envVars = result.parsed;

console.log(`[inject-env] Loaded ${Object.keys(envVars).length} environment variables`);

// Helper to replace placeholders in a file
function replaceInFile(filePath, replacements) {
  if (!fs.existsSync(filePath)) {
    console.warn(`[inject-env] File not found: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let replacementCount = 0;

  Object.entries(replacements).forEach(([key, value]) => {
    const regex = new RegExp(`__${key}__`, 'g');
    const matches = content.match(regex);
    if (matches) {
      content = content.replace(regex, value);
      replacementCount += matches.length;
      console.log(`[inject-env] Replaced ${matches.length} occurrences of __${key}__ with ${value}`);
    }
  });

  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`[inject-env] Updated ${path.basename(filePath)} with ${replacementCount} replacements`);
}

// Common environment variables for all files
const commonReplacements = {
  TENANT_ID: envVars.TENANT_ID,
  CLIENT_ID: envVars.CLIENT_ID,
  CLIENT_SECRET: envVars.CLIENT_SECRET,
  BOT_ID: envVars.BOT_ID,
  BOT_PASSWORD: envVars.BOT_PASSWORD,
  TEAMS_APP_ID: envVars.TEAMS_APP_ID,
  BOT_ENDPOINT: envVars.BOT_ENDPOINT,
  BASE_URL: envVars.BASE_URL,
  SOCKET_URL: envVars.SOCKET_URL,
  REDIRECT_URI: envVars.REDIRECT_URI,
  RESOURCE_URL: envVars.RESOURCE_URL,
  AUTH_URL: envVars.AUTH_URL,
  GRAPH_SCOPES: envVars.GRAPH_SCOPES,
  PORT: envVars.PORT,
  APP_NAME: envVars.APP_NAME,
  PACKAGE_NAME: envVars.PACKAGE_NAME
};

// Restore placeholders first to ensure clean injection
console.log(`[inject-env] Restoring placeholders before injection...`);
restorePlaceholders();

// Update all files
console.log(`[inject-env] Updating files for ${env} environment...`);

replaceInFile(manifestPath, commonReplacements);
replaceInFile(indexPath, commonReplacements);
replaceInFile(serverPath, commonReplacements);
replaceInFile(authEndPath, commonReplacements);
replaceInFile(configPath, commonReplacements);

console.log(`[inject-env] ✅ Successfully injected environment variables for ${env}`);
